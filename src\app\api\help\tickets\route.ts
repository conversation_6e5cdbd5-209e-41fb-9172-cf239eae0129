import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import { HelpTicket, HelpCategory } from '@/models/HelpTicket';
import User from '@/models/User';
import { authMiddleware } from '@/lib/middleware';

interface TicketQuery {
  userId?: string;
  status?: string;
  category?: string;
  priority?: string;
  assignedTo?: string;
  $text?: { $search: string };
}

/**
 * GET /api/help/tickets
 * Get help tickets for the current user or all tickets for admins
 */
export async function GET(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    // Get userId from query parameters (like other working API routes)
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    // Get user to check role
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get query parameters for filtering and pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const priority = searchParams.get('priority');
    const search = searchParams.get('search');
    const assignedTo = searchParams.get('assignedTo');

    // Build query
    const query: TicketQuery = {};
    
    // If not admin, only show user's own tickets
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      query.userId = userId;
    }

    if (status) query.status = status;
    if (category) query.category = category;
    if (priority) query.priority = priority;
    if (assignedTo) query.assignedTo = assignedTo;

    // Add text search if provided
    if (search) {
      query.$text = { $search: search };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get tickets with populated fields
    const tickets = await HelpTicket.find(query)
      .populate('userId', 'name email profileImage')
      .populate('assignedTo', 'name email')
      .populate('lastResponseBy', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await HelpTicket.countDocuments(query);

    return NextResponse.json({
      tickets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching help tickets:', error);
    return NextResponse.json(
      { error: 'Failed to fetch help tickets' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/help/tickets
 * Create a new help ticket
 */
export async function POST(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    // Get request body and extract data
    const body = await request.json();
    const {
      userId,
      category,
      priority = 'medium',
      subject,
      description,
      attachments = [],
      tags = [],
      metadata = {}
    } = body;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Validate required fields
    if (!category || !subject || !description) {
      return NextResponse.json(
        { error: 'Category, subject, and description are required' },
        { status: 400 }
      );
    }

    // Validate category against allowed values
    const allowedCategories = ['subscription', 'technical', 'billing', 'content', 'account', 'bug_report', 'feature_request', 'other'];
    if (!allowedCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      );
    }

    // Create the ticket
    const ticket = new HelpTicket({
      userId,
      userEmail: user.email,
      userName: user.name,
      category,
      priority,
      subject,
      description,
      attachments,
      tags,
      metadata: {
        ...metadata,
        subscriptionStatus: user.subscriptionStatus,
        lastLoginDate: user.lastLogin
      }
    });

    await ticket.save();

    // Update category ticket count
    await HelpCategory.findByIdAndUpdate(
      categoryExists._id,
      { $inc: { ticketCount: 1 } }
    );

    // Populate the ticket before returning
    const populatedTicket = await HelpTicket.findById(ticket._id)
      .populate('userId', 'name email profileImage')
      .lean();

    return NextResponse.json({
      message: 'Help ticket created successfully',
      ticket: populatedTicket
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating help ticket:', error);
    return NextResponse.json(
      { error: 'Failed to create help ticket' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/help/tickets
 * Bulk update tickets (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Use the same authentication pattern as other API routes
    const authResult = await authMiddleware(request);
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.user;
    const userId = user._id.toString();

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { ticketIds, updates } = body;

    if (!ticketIds || !Array.isArray(ticketIds) || ticketIds.length === 0) {
      return NextResponse.json(
        { error: 'Ticket IDs are required' },
        { status: 400 }
      );
    }

    // Update tickets
    const result = await HelpTicket.updateMany(
      { _id: { $in: ticketIds } },
      { 
        ...updates,
        updatedAt: new Date()
      }
    );

    return NextResponse.json({
      message: `Updated ${result.modifiedCount} tickets`,
      modifiedCount: result.modifiedCount
    });

  } catch (error) {
    console.error('Error bulk updating tickets:', error);
    return NextResponse.json(
      { error: 'Failed to update tickets' },
      { status: 500 }
    );
  }
}
